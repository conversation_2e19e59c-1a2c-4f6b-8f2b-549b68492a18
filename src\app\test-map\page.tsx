"use client";

import React, { useEffect, useRef } from "react";
import mapboxgl from "mapbox-gl";

export default function TestMapPage() {
  const mapContainerRef = useRef<HTMLDivElement>(null);
  const mapRef = useRef<mapboxgl.Map | null>(null);

  useEffect(() => {
    console.log("🚀 测试页面开始初始化地图...");
    
    if (!mapContainerRef.current) {
      console.error("❌ 地图容器引用不存在");
      return;
    }

    const mapboxToken = process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN;
    console.log("🔑 Mapbox令牌:", mapboxToken ? "已设置" : "未设置");

    if (!mapboxToken) {
      console.error("❌ Mapbox访问令牌未设置");
      return;
    }

    try {
      // 设置Mapbox访问令牌
      mapboxgl.accessToken = mapboxToken;

      // 创建地图实例
      console.log("🗺️ 创建测试地图实例...");
      mapRef.current = new mapboxgl.Map({
        container: mapContainerRef.current,
        style: "mapbox://styles/mapbox/light-v11",
        center: [-119.4179, 36.7783], // 加利福尼亚中心
        zoom: 6,
      });

      const map = mapRef.current;

      map.on("load", () => {
        console.log("✅ 测试地图加载成功！");
        console.log("📊 地图状态:", {
          loaded: map.loaded(),
          center: map.getCenter(),
          zoom: map.getZoom(),
        });
      });

      map.on("error", (e) => {
        console.error("❌ 测试地图错误:", e);
      });

    } catch (error) {
      console.error("❌ 测试地图初始化异常:", error);
    }

    return () => {
      if (mapRef.current) {
        mapRef.current.remove();
      }
    };
  }, []);

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">地图测试页面</h1>
      <p className="mb-4">这是一个简单的地图测试页面，用于验证Mapbox是否正常工作。</p>
      <div
        ref={mapContainerRef}
        className="w-full h-96 border border-gray-300 rounded"
      />
    </div>
  );
}
