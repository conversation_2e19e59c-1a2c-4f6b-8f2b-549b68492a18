import { useCallback, useState } from "react";

/**
 * 地图状态接口
 */
export interface MapState {
  isMapLoaded: boolean;
  isLoading: boolean;
  loadingStatus: string;
  isShowingBoundaries: boolean;
}

/**
 * 数据状态接口
 */
export interface DataState {
  showZipBoundaries: boolean;
  zipBoundariesData: any;
  isZipDataLoaded: boolean;
}

/**
 * 地图状态管理 Hook
 */
export const useMapState = () => {
  const [mapState, setMapState] = useState<MapState>({
    isMapLoaded: false,
    isLoading: true,
    loadingStatus: "",
    isShowingBoundaries: false,
  });

  const [dataState, setDataState] = useState<DataState>({
    showZipBoundaries: false,
    zipBoundariesData: null,
    isZipDataLoaded: false,
  });

  // 更新地图状态的辅助函数
  const updateMapState = useCallback((updates: Partial<MapState>) => {
    setMapState((prev) => ({ ...prev, ...updates }));
  }, []);

  // 更新数据状态的辅助函数
  const updateDataState = useCallback((updates: Partial<DataState>) => {
    setDataState((prev) => ({ ...prev, ...updates }));
  }, []);

  // 重置所有状态
  const resetState = useCallback(() => {
    setMapState({
      isMapLoaded: false,
      isLoading: true,
      loadingStatus: "",
      isShowingBoundaries: false,
    });
    setDataState({
      showZipBoundaries: false,
      zipBoundariesData: null,
      isZipDataLoaded: false,
    });
  }, []);

  return {
    mapState,
    dataState,
    updateMapState,
    updateDataState,
    resetState,
  };
};
