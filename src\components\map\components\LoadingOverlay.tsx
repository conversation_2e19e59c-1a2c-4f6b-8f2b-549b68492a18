import React from "react";
import { useMapLanguage } from "@/hooks/useMapLanguage";
import type { MapState } from "../hooks/useMapState";

interface LoadingOverlayProps {
  mapState: MapState;
}

/**
 * 地图加载覆盖层组件
 */
const LoadingOverlay: React.FC<LoadingOverlayProps> = ({ mapState }) => {
  const { t } = useMapLanguage();

  if (!mapState.isLoading) {
    return null;
  }

  return (
    <div className="map-loading-overlay">
      <div className="text-center">
        <div className="map-loading-spinner mb-4"></div>
        <div className="text-gray-700 font-medium">{mapState.loadingStatus}</div>
        <div className="text-gray-500 text-sm mt-2">
          {t("map:loading.pleaseWait")}
        </div>
      </div>
    </div>
  );
};

export default LoadingOverlay;
