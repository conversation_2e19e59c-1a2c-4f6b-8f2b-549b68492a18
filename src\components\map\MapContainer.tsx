"use client";

import mapboxgl from "mapbox-gl";
import React, { useCallback, useEffect, useMemo, useRef } from "react";
import { useMapLanguage } from "@/hooks/useMapLanguage";
import type { MapContainerProps } from "@/types/daycare";
import { CALIFORNIA_CENTER, CALIFORNIA_BOUNDS } from "@/types/daycare";

// 导入拆分的模块
import LoadingOverlay from "./components/LoadingOverlay";
import MapControls from "./components/MapControls";
import MapStyles from "./components/MapStyles";
import { MAP_CONFIG, LAYER_IDS, SOURCE_IDS } from "./config/mapConfig";
import { useMapData } from "./hooks/useMapData";
import { useMapState } from "./hooks/useMapState";
import { MapEventHandler } from "./utils/mapEvents";
import { MapLayerManager } from "./utils/mapLayers";

import "mapbox-gl/dist/mapbox-gl.css";

/**
 * 地图容器组件 - 负责Mapbox地图的初始化和数据展示
 * 优化版本：使用React.memo、useCallback、useMemo进行性能优化
 */
const MapContainer: React.FC<MapContainerProps> = ({
  className = "",
  height = "100%",
  onMapLoad,
  onError,
}) => {
  const { t } = useMapLanguage();

  // Refs
  const mapContainerRef = useRef<HTMLDivElement>(null);
  const mapRef = useRef<mapboxgl.Map | null>(null);
  const layerManagerRef = useRef<MapLayerManager | null>(null);
  const eventHandlerRef = useRef<MapEventHandler | null>(null);

  // 使用拆分的状态管理
  const { mapState, dataState, updateMapState, updateDataState } =
    useMapState();

  // Memoized values
  const mapboxToken = useMemo(
    () => process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN,
    []
  );

  const mapOptions = useMemo(
    () => ({
      container: mapContainerRef.current!,
      style: MAP_CONFIG.STYLE,
      center: CALIFORNIA_CENTER,
      zoom: MAP_CONFIG.ZOOM,
      maxBounds: CALIFORNIA_BOUNDS,
      attributionControl: false,
    }),
    []
  );

  // 初始化地图
  const initializeMap = useCallback(async () => {
    if (!mapContainerRef.current || !mapboxToken) {
      return;
    }

    updateMapState({
      loadingStatus: t("map:loading.initializing"),
      isLoading: true,
    });

    try {
      // 设置Mapbox访问令牌
      mapboxgl.accessToken = mapboxToken;

      // 创建地图实例
      mapRef.current = new mapboxgl.Map(mapOptions);
      const map = mapRef.current;

      // 添加控件
      map.addControl(new mapboxgl.NavigationControl(), "top-right");
      map.addControl(
        new mapboxgl.ScaleControl({
          maxWidth: 100,
          unit: "metric",
        }),
        "bottom-right"
      );

      // 初始化图层管理器和事件处理器
      layerManagerRef.current = new MapLayerManager(map);
      eventHandlerRef.current = new MapEventHandler(map);

      // 地图加载完成事件
      map.on("load", async () => {
        updateMapState({
          isMapLoaded: true,
          loadingStatus: t("map:loading.loadingBoundaries"),
        });

        // 设置事件监听器
        eventHandlerRef.current?.setupClickEvents();
        eventHandlerRef.current?.setupHoverEffects();

        // 先预加载ZIP边界数据，然后加载托儿所数据
        const zipData = await preloadZipBoundaries();

        updateMapState({ loadingStatus: t("map:loading.loadingData") });
        await loadDaycareData(zipData);

        updateMapState({
          loadingStatus: t("map:loading.complete"),
          isLoading: false,
        });
        onMapLoad?.();
      });

      // 错误处理
      map.on("error", (_e) => {
        onError?.(new Error("地图加载失败"));
      });
    } catch (error) {
      updateMapState({
        loadingStatus: "地图加载失败",
        isLoading: false,
      });
      onError?.(error as Error);
    }
  }, [mapboxToken, mapOptions, t, onMapLoad, onError, updateMapState]);

  useEffect(() => {
    initializeMap();

    // 清理函数
    return () => {
      if (eventHandlerRef.current) {
        eventHandlerRef.current.cleanup();
      }
      if (mapRef.current) {
        mapRef.current.remove();
        mapRef.current = null;
      }
      layerManagerRef.current = null;
      eventHandlerRef.current = null;
    };
  }, [initializeMap]);

  // 将ZIP区域数据添加到地图
  const addZipAreasToMap = useCallback((mergedData: any) => {
    if (layerManagerRef.current) {
      layerManagerRef.current.addZipAreasLayer(mergedData);
    }
  }, []);

  // 加载点数据（回退方案）
  const loadPointData = useCallback(async () => {
    try {
      const response = await fetch("/api/daycare-data-2023?format=geojson");
      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || "点数据加载失败");
      }

      const daycareGeoJsonData = result.data;
      if (layerManagerRef.current) {
        layerManagerRef.current.addPointDataLayer(daycareGeoJsonData);
      }
    } catch (error) {
      onError?.(error as Error);
    }
  }, [onError]);

  // 使用数据加载 Hook
  const { preloadZipBoundaries, loadDaycareData } = useMapData(
    dataState,
    updateDataState,
    updateMapState,
    addZipAreasToMap,
    loadPointData
  );

  // 将ZIP边界添加到地图
  const addZipBoundariesToMap = useCallback(
    async (geoJsonData?: any) => {
      if (!mapRef.current) {
        return;
      }

      const map = mapRef.current;

      // 使用传入的数据或预加载的数据
      const dataToUse = geoJsonData || dataState.zipBoundariesData;

      if (!dataToUse) {
        return;
      }

      // 设置显示状态
      updateMapState({ isShowingBoundaries: true });

      // 使用 requestAnimationFrame 来避免阻塞UI
      await new Promise((resolve) => requestAnimationFrame(resolve));

      // 检查并移除已存在的ZIP边界图层和数据源
      if (map.getLayer("zip-boundaries-fill")) {
        map.removeLayer("zip-boundaries-fill");
      }
      if (map.getLayer("zip-boundaries-line")) {
        map.removeLayer("zip-boundaries-line");
      }
      if (map.getSource("zip-boundaries")) {
        map.removeSource("zip-boundaries");
      }

      // 添加ZIP边界数据源
      map.addSource("zip-boundaries", {
        type: "geojson",
        data: dataToUse,
        // 性能优化选项
        lineMetrics: false,
        generateId: true,
        buffer: 0, // 减少缓冲区
        tolerance: 0.375, // 简化几何形状
      });

      // 使用 requestAnimationFrame 分批添加图层
      await new Promise((resolve) => requestAnimationFrame(resolve));

      // 添加填充图层（半透明）
      map.addLayer({
        id: "zip-boundaries-fill",
        type: "fill",
        source: "zip-boundaries",
        paint: {
          "fill-color": "#3b82f6",
          "fill-opacity": [
            "interpolate",
            ["linear"],
            ["zoom"],
            6,
            0.05, // 低缩放级别时更透明
            10,
            0.1, // 中等缩放级别
            14,
            0.15, // 高缩放级别时更明显
          ],
        },
        // 只在特定缩放级别显示以提高性能
        minzoom: 6,
      });

      await new Promise((resolve) => requestAnimationFrame(resolve));

      // 添加边界线图层
      map.addLayer({
        id: "zip-boundaries-line",
        type: "line",
        source: "zip-boundaries",
        paint: {
          "line-color": "#3b82f6",
          "line-width": [
            "interpolate",
            ["linear"],
            ["zoom"],
            6,
            0.3, // 低缩放级别时更细
            10,
            0.8, // 中等缩放级别
            14,
            1.5, // 高缩放级别时更粗
          ],
          "line-opacity": [
            "interpolate",
            ["linear"],
            ["zoom"],
            6,
            0.4, // 低缩放级别时更透明
            10,
            0.7, // 中等缩放级别
            14,
            0.9, // 高缩放级别时更明显
          ],
        },
        minzoom: 6,
      });

      updateMapState({ isShowingBoundaries: false });
    },
    [dataState.zipBoundariesData, updateMapState]
  );

  // 显示ZIP边界（使用预加载的数据）
  const showZipBoundariesOnMap = useCallback(async () => {
    if (!mapRef.current) {
      return;
    }

    if (!dataState.isZipDataLoaded || !dataState.zipBoundariesData) {
      // 如果数据还没加载，先加载数据
      const data = await preloadZipBoundaries();
      if (data) {
        await addZipBoundariesToMap(data);
      }
      return;
    }

    await addZipBoundariesToMap();
  }, [
    dataState.isZipDataLoaded,
    dataState.zipBoundariesData,
    preloadZipBoundaries,
    addZipBoundariesToMap,
  ]);

  // 隐藏ZIP边界
  const hideZipBoundaries = useCallback(() => {
    if (mapRef.current) {
      const map = mapRef.current;
      if (map.getLayer(LAYER_IDS.ZIP_BOUNDARIES_FILL)) {
        map.removeLayer(LAYER_IDS.ZIP_BOUNDARIES_FILL);
      }
      if (map.getLayer(LAYER_IDS.ZIP_BOUNDARIES_LINE)) {
        map.removeLayer(LAYER_IDS.ZIP_BOUNDARIES_LINE);
      }
      if (map.getSource(SOURCE_IDS.ZIP_BOUNDARIES)) {
        map.removeSource(SOURCE_IDS.ZIP_BOUNDARIES);
      }
    }
  }, []);

  // 切换ZIP边界显示
  const toggleZipBoundaries = useCallback(() => {
    const newShowState = !dataState.showZipBoundaries;
    updateDataState({ showZipBoundaries: newShowState });

    if (newShowState) {
      showZipBoundariesOnMap();
    } else {
      hideZipBoundaries();
    }
  }, [
    dataState.showZipBoundaries,
    updateDataState,
    showZipBoundariesOnMap,
    hideZipBoundaries,
  ]);

  // 飞行到指定位置
  const flyTo = useCallback(
    (coordinates: [number, number], zoom: number = 12) => {
      if (!mapRef.current) {
        return;
      }

      mapRef.current.flyTo({
        center: coordinates,
        zoom,
        duration: 2000,
      });
    },
    []
  );

  // 监听自定义事件来处理飞行到指定位置
  useEffect(() => {
    const handleMapFlyTo = (event: CustomEvent) => {
      const { coordinates, zoom } = event.detail;
      flyTo(coordinates, zoom);
    };

    window.addEventListener("mapFlyTo", handleMapFlyTo as EventListener);

    return () => {
      window.removeEventListener("mapFlyTo", handleMapFlyTo as EventListener);
    };
  }, [flyTo]);

  return (
    <>
      <div className="relative" style={{ height }}>
        <div
          ref={mapContainerRef}
          className={`map-container ${className}`}
          style={{ height: "100%", width: "100%" }}
        />

        <LoadingOverlay mapState={mapState} />

        <MapControls
          dataState={dataState}
          mapState={mapState}
          onToggleZipBoundaries={toggleZipBoundaries}
        />
      </div>

      <MapStyles />
    </>
  );
};

// 使用React.memo进行性能优化，避免不必要的重新渲染
export default React.memo(MapContainer);
