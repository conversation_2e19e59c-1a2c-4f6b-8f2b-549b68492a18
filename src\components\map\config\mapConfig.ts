/**
 * 地图配置常量
 */

export const MAP_CONFIG = {
  STYLE: "mapbox://styles/mapbox/light-v11",
  ZOOM: 7,
  TIMEOUT: 10000,
  BUFFER: 0,
  TOLERANCE: 0.375,
} as const;

export const LAYER_IDS = {
  ZIP_AREAS: "daycare-zip-areas",
  ZIP_AREAS_STROKE: "daycare-zip-areas-stroke",
  ZIP_AREAS_LABELS: "daycare-zip-areas-labels",
  ZIP_BOUNDARIES_FILL: "zip-boundaries-fill",
  ZIP_BOUNDARIES_LINE: "zip-boundaries-line",
  DAYCARE_CIRCLES: "daycare-circles",
} as const;

export const SOURCE_IDS = {
  ZIP_AREAS: "daycare-zip-areas",
  ZIP_BOUNDARIES: "zip-boundaries",
  DAYCARE_DATA: "daycare-data-2023",
} as const;

export const POPUP_CONFIG = {
  MAX_WIDTH: 320,
  CLOSE_ON_CLICK: false,
  CLOSE_ON_MOVE: false,
} as const;

export const LAYER_STYLES = {
  ZIP_AREA_FILL: {
    "fill-color": [
      "case",
      ["==", ["get", "saturation_level"], "high"],
      "#dc2626", // 红色 - 高饱和度
      ["==", ["get", "saturation_level"], "medium"],
      "#f59e0b", // 橙色 - 中等饱和度
      "#22c55e", // 绿色 - 低饱和度
    ],
    "fill-opacity": [
      "interpolate",
      ["linear"],
      ["zoom"],
      6,
      0.4, // 低缩放级别时更透明
      10,
      0.7, // 中等缩放级别
      14,
      0.9, // 高缩放级别时更明显
    ],
  },
  ZIP_AREA_STROKE: {
    "line-color": "#ffffff",
    "line-width": [
      "interpolate",
      ["linear"],
      ["zoom"],
      6,
      0.5,
      10,
      1,
      14,
      2,
    ],
    "line-opacity": 0.8,
  },
  ZIP_BOUNDARIES: {
    FILL: {
      "fill-color": "rgba(59, 130, 246, 0.1)",
      "fill-opacity": [
        "interpolate",
        ["linear"],
        ["zoom"],
        6,
        0.4,
        10,
        0.7,
        14,
        0.9,
      ],
    },
    LINE: {
      "line-color": "#3b82f6",
      "line-width": [
        "interpolate",
        ["linear"],
        ["zoom"],
        6,
        0.5,
        10,
        1,
        14,
        2,
      ],
      "line-opacity": 0.8,
    },
  },
  DAYCARE_CIRCLES: {
    "circle-radius": [
      "interpolate",
      ["linear"],
      ["zoom"],
      6,
      2,
      10,
      4,
      14,
      8,
    ],
    "circle-color": [
      "case",
      ["==", ["get", "saturation_level"], "high"],
      "#dc2626",
      ["==", ["get", "saturation_level"], "medium"],
      "#f59e0b",
      "#22c55e",
    ],
    "circle-opacity": 0.8,
    "circle-stroke-width": 1,
    "circle-stroke-color": "#ffffff",
  },
} as const;
