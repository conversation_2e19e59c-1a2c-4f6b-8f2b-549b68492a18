import React from "react";
import { useMapLanguage } from "@/hooks/useMapLanguage";
import type { DataState, MapState } from "../hooks/useMapState";

interface MapControlsProps {
  dataState: DataState;
  mapState: MapState;
  onToggleZipBoundaries: () => void;
}

/**
 * 地图控制按钮组件
 */
const MapControls: React.FC<MapControlsProps> = ({
  dataState,
  mapState,
  onToggleZipBoundaries,
}) => {
  const { t } = useMapLanguage();

  const isDisabled =
    (!dataState.isZipDataLoaded && !dataState.showZipBoundaries) ||
    mapState.isShowingBoundaries;

  const buttonText = (() => {
    if (!dataState.isZipDataLoaded && !dataState.showZipBoundaries) {
      return t("map:common.loading");
    }
    if (mapState.isShowingBoundaries) {
      return t("map:loading.rendering");
    }
    return dataState.showZipBoundaries
      ? t("map:boundaries.hide")
      : t("map:boundaries.show");
  })();

  const buttonTitle = dataState.showZipBoundaries
    ? t("map:boundaries.hide")
    : t("map:boundaries.show");

  return (
    <button
      onClick={onToggleZipBoundaries}
      className={`map-control-button ${
        dataState.showZipBoundaries ? "active" : "inactive"
      }`}
      title={buttonTitle}
      disabled={isDisabled}
      aria-label={buttonTitle}
    >
      {buttonText}
    </button>
  );
};

export default MapControls;
