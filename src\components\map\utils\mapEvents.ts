import mapboxgl from "mapbox-gl";
import { LAYER_IDS, POPUP_CONFIG } from "../config/mapConfig";

/**
 * 地图事件处理工具类
 */
export class MapEventHandler {
  private map: mapboxgl.Map;
  private popup: mapboxgl.Popup | null = null;

  constructor(map: mapboxgl.Map) {
    this.map = map;
    this.initializePopup();
  }

  /**
   * 初始化弹窗
   */
  private initializePopup(): void {
    this.popup = new mapboxgl.Popup({
      maxWidth: `${POPUP_CONFIG.MAX_WIDTH}px`,
      closeOnClick: POPUP_CONFIG.CLOSE_ON_CLICK,
      closeOnMove: POPUP_CONFIG.CLOSE_ON_MOVE,
    });
  }

  /**
   * 处理ZIP区域点击事件
   */
  handleZipAreaClick = (
    e: mapboxgl.MapMouseEvent & { features?: mapboxgl.MapboxGeoJSONFeature[] }
  ): void => {
    if (!e.features || e.features.length === 0) return;

    const feature = e.features[0];
    const properties = feature.properties;

    if (!properties) return;

    const popupContent = this.createZipAreaPopupContent(properties);

    this.popup
      ?.setLngLat(e.lngLat)
      .setHTML(popupContent)
      .addTo(this.map);
  };

  /**
   * 处理点数据点击事件
   */
  handlePointDataClick = (
    e: mapboxgl.MapMouseEvent & { features?: mapboxgl.MapboxGeoJSONFeature[] }
  ): void => {
    if (!e.features || e.features.length === 0) return;

    const feature = e.features[0];
    const properties = feature.properties;

    if (!properties) return;

    const popupContent = this.createPointDataPopupContent(properties);

    this.popup
      ?.setLngLat(e.lngLat)
      .setHTML(popupContent)
      .addTo(this.map);
  };

  /**
   * 创建ZIP区域弹窗内容
   */
  private createZipAreaPopupContent(properties: any): string {
    const zipCode = properties.zip_code || "未知";
    const totalCount = properties.total_count || 0;
    const saturationLevel = properties.saturation_level || "unknown";

    const saturationText = {
      high: "高饱和度",
      medium: "中等饱和度",
      low: "低饱和度",
      unknown: "未知",
    }[saturationLevel] || "未知";

    const saturationColor = {
      high: "#dc2626",
      medium: "#f59e0b",
      low: "#22c55e",
      unknown: "#6b7280",
    }[saturationLevel] || "#6b7280";

    return `
      <div class="p-4 bg-white rounded-lg shadow-lg">
        <div class="flex items-center justify-between mb-3">
          <h3 class="text-lg font-semibold text-gray-900">ZIP ${zipCode}</h3>
          <span 
            class="px-2 py-1 text-xs font-medium text-white rounded-full"
            style="background-color: ${saturationColor}"
          >
            ${saturationText}
          </span>
        </div>
        <div class="space-y-2">
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-600">托儿所数量:</span>
            <span class="text-sm font-medium text-gray-900">${totalCount}</span>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * 创建点数据弹窗内容
   */
  private createPointDataPopupContent(properties: any): string {
    const zipCode = properties.zip_code || "未知";
    const totalCount = properties.total_count || 0;
    const saturationLevel = properties.saturation_level || "unknown";

    const saturationText = {
      high: "高饱和度",
      medium: "中等饱和度",
      low: "低饱和度",
      unknown: "未知",
    }[saturationLevel] || "未知";

    const saturationColor = {
      high: "#dc2626",
      medium: "#f59e0b",
      low: "#22c55e",
      unknown: "#6b7280",
    }[saturationLevel] || "#6b7280";

    return `
      <div class="p-4 bg-white rounded-lg shadow-lg">
        <div class="flex items-center justify-between mb-3">
          <h3 class="text-lg font-semibold text-gray-900">ZIP ${zipCode}</h3>
          <span 
            class="px-2 py-1 text-xs font-medium text-white rounded-full"
            style="background-color: ${saturationColor}"
          >
            ${saturationText}
          </span>
        </div>
        <div class="space-y-2">
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-600">托儿所数量:</span>
            <span class="text-sm font-medium text-gray-900">${totalCount}</span>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * 设置鼠标悬停效果
   */
  setupHoverEffects(): void {
    // ZIP区域悬停效果
    this.map.on("mouseenter", LAYER_IDS.ZIP_AREAS, () => {
      this.map.getCanvas().style.cursor = "pointer";
    });

    this.map.on("mouseleave", LAYER_IDS.ZIP_AREAS, () => {
      this.map.getCanvas().style.cursor = "";
    });

    // 点数据悬停效果
    this.map.on("mouseenter", LAYER_IDS.DAYCARE_CIRCLES, () => {
      this.map.getCanvas().style.cursor = "pointer";
    });

    this.map.on("mouseleave", LAYER_IDS.DAYCARE_CIRCLES, () => {
      this.map.getCanvas().style.cursor = "";
    });
  }

  /**
   * 设置点击事件监听器
   */
  setupClickEvents(): void {
    this.map.on("click", LAYER_IDS.ZIP_AREAS, this.handleZipAreaClick);
    this.map.on("click", LAYER_IDS.DAYCARE_CIRCLES, this.handlePointDataClick);
  }

  /**
   * 移除事件监听器
   */
  removeEventListeners(): void {
    this.map.off("click", LAYER_IDS.ZIP_AREAS, this.handleZipAreaClick);
    this.map.off("click", LAYER_IDS.DAYCARE_CIRCLES, this.handlePointDataClick);
    this.map.off("mouseenter", LAYER_IDS.ZIP_AREAS);
    this.map.off("mouseleave", LAYER_IDS.ZIP_AREAS);
    this.map.off("mouseenter", LAYER_IDS.DAYCARE_CIRCLES);
    this.map.off("mouseleave", LAYER_IDS.DAYCARE_CIRCLES);
  }

  /**
   * 关闭弹窗
   */
  closePopup(): void {
    if (this.popup && this.popup.isOpen()) {
      this.popup.remove();
    }
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.removeEventListeners();
    this.closePopup();
    this.popup = null;
  }
}
