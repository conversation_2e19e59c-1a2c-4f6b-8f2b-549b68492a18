import React from "react";

/**
 * 地图样式组件 - 包含所有地图相关的CSS样式
 */
const MapStyles: React.FC = () => {
  return (
    <style jsx global>{`
      /* Mapbox弹窗基础样式 */
      .mapboxgl-popup {
        max-width: 320px !important;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif !important;
      }

      .mapboxgl-popup-content {
        padding: 0 !important;
        border-radius: 16px !important;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15),
          0 4px 12px rgba(0, 0, 0, 0.1) !important;
        border: 1px solid rgba(0, 0, 0, 0.05) !important;
        background: white !important;
        backdrop-filter: blur(10px) !important;
        -webkit-backdrop-filter: blur(10px) !important;
        overflow: hidden !important;
        animation: popupFadeIn 0.2s ease-out !important;
        transform-origin: bottom center !important;
        transition: none !important;
      }

      .mapboxgl-popup-close-button {
        display: none !important;
      }

      .mapboxgl-popup-tip {
        border-top-color: white !important;
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1)) !important;
      }

      /* 弹窗动画 */
      @keyframes popupFadeIn {
        0% {
          opacity: 0;
          transform: scale(0.9) translateY(10px);
        }
        100% {
          opacity: 1;
          transform: scale(1) translateY(0);
        }
      }

      /* 地图容器样式 */
      .map-container {
        position: relative;
        width: 100%;
        height: 100%;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      }

      /* 加载状态样式 */
      .map-loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(4px);
        -webkit-backdrop-filter: blur(4px);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 20;
        animation: fadeIn 0.3s ease-out;
      }

      @keyframes fadeIn {
        from {
          opacity: 0;
        }
        to {
          opacity: 1;
        }
      }

      /* 加载动画 */
      .map-loading-spinner {
        width: 32px;
        height: 32px;
        border: 2px solid #e5e7eb;
        border-top: 2px solid #3b82f6;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      /* 控制按钮样式 */
      .map-control-button {
        position: absolute;
        top: 12px;
        right: 60px;
        z-index: 10;
        padding: 8px 12px;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.2s ease-in-out;
        border: none;
        cursor: pointer;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      .map-control-button:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      .map-control-button:active {
        transform: translateY(0);
      }

      .map-control-button:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none !important;
      }

      .map-control-button.active {
        background: #3b82f6 !important;
        color: white !important;
      }

      .map-control-button.inactive {
        background: white;
        color: #374151;
        border: 1px solid #d1d5db;
      }

      /* 响应式设计 */
      @media (max-width: 768px) {
        .map-control-button {
          top: 8px;
          right: 8px;
          padding: 6px 10px;
          font-size: 12px;
        }

        .mapboxgl-popup {
          max-width: 280px !important;
        }
      }

      /* 高对比度模式支持 */
      @media (prefers-contrast: high) {
        .mapboxgl-popup-content {
          border: 2px solid #000 !important;
        }

        .map-control-button {
          border: 2px solid #000 !important;
        }
      }

      /* 减少动画模式支持 */
      @media (prefers-reduced-motion: reduce) {
        .mapboxgl-popup-content,
        .map-loading-overlay,
        .map-control-button {
          animation: none !important;
          transition: none !important;
        }
      }

      /* 深色模式支持 */
      @media (prefers-color-scheme: dark) {
        .mapboxgl-popup-content {
          background: #1f2937 !important;
          color: white !important;
          border-color: #374151 !important;
        }

        .mapboxgl-popup-tip {
          border-top-color: #1f2937 !important;
        }

        .map-loading-overlay {
          background: rgba(31, 41, 55, 0.9) !important;
        }

        .map-control-button.inactive {
          background: #374151;
          color: white;
          border-color: #4b5563;
        }
      }
    `}</style>
  );
};

export default MapStyles;
