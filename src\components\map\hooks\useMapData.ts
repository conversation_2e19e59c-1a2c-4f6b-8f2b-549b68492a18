import { useCallback, useRef } from "react";
import { mergeZipBoundariesWithPropertiesData } from "@/utils/zipCodeCoordinates";
import { MAP_CONFIG } from "../config/mapConfig";
import type { DataState, MapState } from "./useMapState";

/**
 * 数据加载相关的 Hook
 */
export const useMapData = (
  dataState: DataState,
  updateDataState: (updates: Partial<DataState>) => void,
  updateMapState: (updates: Partial<MapState>) => void,
  addZipAreasToMap: (data: any) => void,
  loadPointData: () => Promise<void>
) => {
  const abortControllerRef = useRef<AbortController | null>(null);

  // 预加载ZIP边界数据（不显示在地图上）
  const preloadZipBoundaries = useCallback(async () => {
    console.log("🔄 开始预加载ZIP边界数据（简化版本）...");

    if (dataState.isZipDataLoaded) {
      console.log("✅ ZIP数据已加载，直接返回缓存数据");
      return dataState.zipBoundariesData;
    }

    try {
      // 创建一个带超时的fetch请求
      console.log("📡 创建API请求到 /api/zip-boundaries?simplified=true");
      abortControllerRef.current = new AbortController();
      const timeoutId = setTimeout(() => {
        abortControllerRef.current?.abort();
      }, MAP_CONFIG.TIMEOUT);

      const response = await fetch("/api/zip-boundaries?simplified=true", {
        signal: abortControllerRef.current.signal,
      });
      clearTimeout(timeoutId);

      console.log("📡 API响应状态:", response.status, response.statusText);
      const result = await response.json();
      console.log("📊 ZIP边界数据响应:", {
        success: result.success,
        count: result.count,
        source: result.source,
        featuresCount: result.data?.features?.length || 0,
      });

      if (!result.success) {
        console.warn("⚠️ ZIP边界数据加载失败:", result.error);
        return null;
      }

      updateDataState({
        zipBoundariesData: result.data,
        isZipDataLoaded: true,
      });

      // 提取ZIP坐标映射
      updateMapState({ loadingStatus: "计算ZIP坐标..." });
      console.log(
        "✅ ZIP边界数据预加载成功，features数量:",
        result.data.features?.length || 0
      );

      return result.data;
    } catch (error) {
      if (error instanceof Error && error.name === "AbortError") {
        updateMapState({ loadingStatus: "ZIP数据加载超时" });
      } else {
        updateMapState({ loadingStatus: "ZIP数据加载失败" });
      }
      return null;
    }
  }, [
    dataState.isZipDataLoaded,
    dataState.zipBoundariesData,
    updateDataState,
    updateMapState,
  ]);

  // 加载托儿所数据并与ZIP边界合并
  const loadDaycareData = useCallback(
    async (zipData?: any): Promise<void> => {
      console.log("🔄 开始加载托儿所数据...");
      try {
        // 使用传入的ZIP数据或状态中的ZIP数据
        const availableZipData = zipData || dataState.zipBoundariesData;
        console.log("🔄 可用的ZIP数据:", {
          hasZipData: !!availableZipData,
          featuresCount: availableZipData?.features?.length || 0,
        });

        // 如果ZIP边界数据已加载，使用属性数据创建ZIP区域显示
        if (availableZipData) {
          console.log("🔄 开始获取托儿所属性数据...");
          updateMapState({ loadingStatus: "正在加载托儿所属性数据..." });

          const propertiesResponse = await fetch(
            "/api/daycare-properties-2023"
          );
          const propertiesResult = await propertiesResponse.json();

          console.log("🔄 托儿所属性数据响应:", {
            success: propertiesResult.success,
            hasData: !!propertiesResult.data,
            dataKeys: propertiesResult.data
              ? Object.keys(propertiesResult.data).length
              : 0,
          });

          if (!propertiesResult.success) {
            throw new Error(propertiesResult.error || "属性数据加载失败");
          }

          console.log("🔄 开始合并ZIP边界和托儿所数据...");
          updateMapState({ loadingStatus: "正在合并数据..." });

          const mergedData = mergeZipBoundariesWithPropertiesData(
            availableZipData,
            propertiesResult.data
          );

          console.log("🔄 数据合并结果:", {
            hasMergedData: !!mergedData,
            featuresCount: mergedData?.features?.length || 0,
          });

          if (mergedData) {
            console.log("✅ 使用合并后的ZIP区域数据");
            updateMapState({ loadingStatus: "正在添加ZIP区域到地图..." });
            addZipAreasToMap(mergedData);
          } else {
            console.log("⚠️ 数据合并失败，回退到点数据");
            updateMapState({ loadingStatus: "正在加载点数据..." });
            await loadPointData();
          }
        } else {
          console.log("⚠️ 没有ZIP边界数据，使用点数据");
          updateMapState({ loadingStatus: "正在加载点数据..." });
          await loadPointData();
        }
      } catch (error) {
        console.error("❌ 加载托儿所数据时出错:", error);
        updateMapState({ loadingStatus: "数据加载失败，使用点数据..." });
        await loadPointData();
      }
    },
    [
      dataState.zipBoundariesData,
      addZipAreasToMap,
      loadPointData,
      updateMapState,
    ]
  );

  // 清理资源
  const cleanup = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
  }, []);

  return {
    preloadZipBoundaries,
    loadDaycareData,
    cleanup,
  };
};
