import { useCallback, useRef } from "react";
import { mergeZipBoundariesWithPropertiesData } from "@/utils/zipCodeCoordinates";
import { MAP_CONFIG } from "../config/mapConfig";
import type { DataState, MapState } from "./useMapState";

/**
 * 数据加载相关的 Hook
 */
export const useMapData = (
  dataState: DataState,
  updateDataState: (updates: Partial<DataState>) => void,
  updateMapState: (updates: Partial<MapState>) => void,
  addZipAreasToMap: (data: any) => void,
  loadPointData: () => Promise<void>
) => {
  const abortControllerRef = useRef<AbortController | null>(null);

  // 预加载ZIP边界数据（不显示在地图上）
  const preloadZipBoundaries = useCallback(async () => {
    if (dataState.isZipDataLoaded) {
      return dataState.zipBoundariesData;
    }

    try {
      // 创建一个带超时的fetch请求
      abortControllerRef.current = new AbortController();
      const timeoutId = setTimeout(() => {
        abortControllerRef.current?.abort();
      }, MAP_CONFIG.TIMEOUT);

      const response = await fetch("/api/zip-boundaries", {
        signal: abortControllerRef.current.signal,
      });
      clearTimeout(timeoutId);

      const result = await response.json();

      if (!result.success) {
        return null;
      }

      updateDataState({
        zipBoundariesData: result.data,
        isZipDataLoaded: true,
      });

      // 提取ZIP坐标映射
      updateMapState({ loadingStatus: "计算ZIP坐标..." });

      return result.data;
    } catch (error) {
      if (error instanceof Error && error.name === "AbortError") {
        updateMapState({ loadingStatus: "ZIP数据加载超时" });
      } else {
        updateMapState({ loadingStatus: "ZIP数据加载失败" });
      }
      return null;
    }
  }, [dataState.isZipDataLoaded, dataState.zipBoundariesData, updateDataState, updateMapState]);

  // 加载托儿所数据并与ZIP边界合并
  const loadDaycareData = useCallback(
    async (zipData?: any): Promise<void> => {
      try {
        // 使用传入的ZIP数据或状态中的ZIP数据
        const availableZipData = zipData || dataState.zipBoundariesData;

        // 如果ZIP边界数据已加载，使用属性数据创建ZIP区域显示
        if (availableZipData) {
          const propertiesResponse = await fetch("/api/daycare-properties-2023");
          const propertiesResult = await propertiesResponse.json();

          if (!propertiesResult.success) {
            throw new Error(propertiesResult.error || "属性数据加载失败");
          }

          const mergedData = mergeZipBoundariesWithPropertiesData(
            availableZipData,
            propertiesResult.data
          );

          if (mergedData) {
            addZipAreasToMap(mergedData);
          } else {
            await loadPointData();
          }
        } else {
          await loadPointData();
        }
      } catch (error) {
        console.error("Error loading daycare data:", error);
        await loadPointData();
      }
    },
    [dataState.zipBoundariesData, addZipAreasToMap, loadPointData]
  );

  // 清理资源
  const cleanup = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
  }, []);

  return {
    preloadZipBoundaries,
    loadDaycareData,
    cleanup,
  };
};
