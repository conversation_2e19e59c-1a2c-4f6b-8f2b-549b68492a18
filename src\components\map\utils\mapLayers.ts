import mapboxgl from "mapbox-gl";
import { LAYER_IDS, SOURCE_IDS, LAYER_STYLES } from "../config/mapConfig";

/**
 * 地图图层管理工具类
 */
export class MapLayerManager {
  private map: mapboxgl.Map;

  constructor(map: mapboxgl.Map) {
    this.map = map;
  }

  /**
   * 添加ZIP区域图层到地图
   */
  addZipAreasLayer(geoJsonData: any): void {
    // 移除现有的图层和数据源
    this.removeZipAreasLayer();

    // 添加数据源
    this.map.addSource(SOURCE_IDS.ZIP_AREAS, {
      type: "geojson",
      data: geoJsonData,
      buffer: 0,
      tolerance: 0.375,
    });

    // 添加填充图层
    this.map.addLayer({
      id: LAYER_IDS.ZIP_AREAS,
      type: "fill",
      source: SOURCE_IDS.ZIP_AREAS,
      paint: LAYER_STYLES.ZIP_AREA_FILL,
      minzoom: 6,
    });

    // 添加边框图层
    this.map.addLayer({
      id: LAYER_IDS.ZIP_AREAS_STROKE,
      type: "line",
      source: SOURCE_IDS.ZIP_AREAS,
      paint: LAYER_STYLES.ZIP_AREA_STROKE,
      minzoom: 6,
    });

    // 添加标签图层
    this.map.addLayer({
      id: LAYER_IDS.ZIP_AREAS_LABELS,
      type: "symbol",
      source: SOURCE_IDS.ZIP_AREAS,
      layout: {
        "text-field": ["get", "zip_code"],
        "text-font": ["Open Sans Semibold", "Arial Unicode MS Bold"],
        "text-size": [
          "interpolate",
          ["linear"],
          ["zoom"],
          8,
          10,
          12,
          14,
          16,
          18,
        ],
        "text-anchor": "center",
        "text-allow-overlap": false,
        "text-ignore-placement": false,
      },
      paint: {
        "text-color": "#1f2937",
        "text-halo-color": "#ffffff",
        "text-halo-width": 1,
        "text-opacity": [
          "interpolate",
          ["linear"],
          ["zoom"],
          8,
          0.6,
          10,
          0.8,
          12,
          1,
        ],
      },
      minzoom: 8,
    });
  }

  /**
   * 移除ZIP区域图层
   */
  removeZipAreasLayer(): void {
    const layersToRemove = [
      LAYER_IDS.ZIP_AREAS,
      LAYER_IDS.ZIP_AREAS_STROKE,
      LAYER_IDS.ZIP_AREAS_LABELS,
    ];

    layersToRemove.forEach((layerId) => {
      if (this.map.getLayer(layerId)) {
        this.map.removeLayer(layerId);
      }
    });

    if (this.map.getSource(SOURCE_IDS.ZIP_AREAS)) {
      this.map.removeSource(SOURCE_IDS.ZIP_AREAS);
    }
  }

  /**
   * 添加ZIP边界图层
   */
  addZipBoundariesLayer(geoJsonData: any): void {
    this.removeZipBoundariesLayer();

    this.map.addSource(SOURCE_IDS.ZIP_BOUNDARIES, {
      type: "geojson",
      data: geoJsonData,
      buffer: 0,
      tolerance: 0.375,
    });

    // 添加填充图层
    this.map.addLayer({
      id: LAYER_IDS.ZIP_BOUNDARIES_FILL,
      type: "fill",
      source: SOURCE_IDS.ZIP_BOUNDARIES,
      paint: LAYER_STYLES.ZIP_BOUNDARIES.FILL,
      minzoom: 6,
    });

    // 添加边框图层
    this.map.addLayer({
      id: LAYER_IDS.ZIP_BOUNDARIES_LINE,
      type: "line",
      source: SOURCE_IDS.ZIP_BOUNDARIES,
      paint: LAYER_STYLES.ZIP_BOUNDARIES.LINE,
      minzoom: 6,
    });
  }

  /**
   * 移除ZIP边界图层
   */
  removeZipBoundariesLayer(): void {
    const layersToRemove = [
      LAYER_IDS.ZIP_BOUNDARIES_FILL,
      LAYER_IDS.ZIP_BOUNDARIES_LINE,
    ];

    layersToRemove.forEach((layerId) => {
      if (this.map.getLayer(layerId)) {
        this.map.removeLayer(layerId);
      }
    });

    if (this.map.getSource(SOURCE_IDS.ZIP_BOUNDARIES)) {
      this.map.removeSource(SOURCE_IDS.ZIP_BOUNDARIES);
    }
  }

  /**
   * 添加点数据图层
   */
  addPointDataLayer(geoJsonData: any): void {
    this.removePointDataLayer();

    this.map.addSource(SOURCE_IDS.DAYCARE_DATA, {
      type: "geojson",
      data: geoJsonData,
      buffer: 0,
      tolerance: 0.375,
    });

    this.map.addLayer({
      id: LAYER_IDS.DAYCARE_CIRCLES,
      type: "circle",
      source: SOURCE_IDS.DAYCARE_DATA,
      paint: LAYER_STYLES.DAYCARE_CIRCLES,
      minzoom: 6,
    });
  }

  /**
   * 移除点数据图层
   */
  removePointDataLayer(): void {
    if (this.map.getLayer(LAYER_IDS.DAYCARE_CIRCLES)) {
      this.map.removeLayer(LAYER_IDS.DAYCARE_CIRCLES);
    }

    if (this.map.getSource(SOURCE_IDS.DAYCARE_DATA)) {
      this.map.removeSource(SOURCE_IDS.DAYCARE_DATA);
    }
  }

  /**
   * 检查图层是否存在
   */
  hasLayer(layerId: string): boolean {
    return !!this.map.getLayer(layerId);
  }

  /**
   * 检查数据源是否存在
   */
  hasSource(sourceId: string): boolean {
    return !!this.map.getSource(sourceId);
  }
}
